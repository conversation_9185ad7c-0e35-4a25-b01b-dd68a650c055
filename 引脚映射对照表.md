# 药片装瓶系统引脚映射对照表

## 更新说明
✅ **已更新所有约束文件，使用正确的键盘引脚 K1-K8**

## 完整引脚映射表

### 系统基础信号
| 功能 | Verilog端口 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|-------------|----------|----------|--------|------|
| 系统时钟 | sys_clk | Y18 | 100MHz | LVCMOS33 | 主时钟 |
| 系统复位 | sys_rst_n | P20 | S6 | LVCMOS33 | 低电平有效 |
| 开始信号 | start | R1 | S1 | LVCMOS15 | 启动装瓶 |
| 确认信号 | ack | P1 | S2 | LVCMOS15 | 确认输入 |
| 药片模式 | pil_mode | W4 | SW0 | LVCMOS33 | 普通/定制 |
| 显示模式 | display_mode | R4 | SW1 | LVCMOS33 | 显示切换 |

### 4x4矩阵键盘
| 功能 | Verilog端口 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|-------------|----------|----------|--------|------|
| 列0 | col[0] | K4 | K1 | LVCMOS33 | 键盘列输入 |
| 列1 | col[1] | J4 | K2 | LVCMOS33 | 键盘列输入 |
| 列2 | col[2] | L3 | K3 | LVCMOS33 | 键盘列输入 |
| 列3 | col[3] | K3 | K4 | LVCMOS33 | 键盘列输入 |
| 行0 | row[0] | M2 | K5 | LVCMOS33 | 键盘行输出 |
| 行1 | row[1] | K6 | K6 | LVCMOS33 | 键盘行输出 |
| 行2 | row[2] | J6 | K7 | LVCMOS33 | 键盘行输出 |
| 行3 | row[3] | J5 | K8 | LVCMOS33 | 键盘行输出 |

**注意：** K9 (L4引脚) 暂未使用，如有需要可以作为扩展功能

### 数码管显示（8位）
#### 位选信号（低电平有效）
| 功能 | Verilog端口 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|-------------|----------|----------|--------|------|
| 低位个位 | l_gw | C19 | A0 | LVCMOS33 | 最右侧数码管 |
| 低位十位 | l_sw | E19 | A1 | LVCMOS33 | 第2位数码管 |
| 低位百位 | l_bw | D19 | A2 | LVCMOS33 | 第3位数码管 |
| 低位千位 | l_qw | F18 | A3 | LVCMOS33 | 第4位数码管 |
| 高位个位 | h_gw | E18 | A4 | LVCMOS33 | 第5位数码管 |
| 高位十位 | h_sw | B20 | A5 | LVCMOS33 | 第6位数码管 |
| 高位百位 | h_bw | A20 | A6 | LVCMOS33 | 第7位数码管 |
| 高位千位 | h_qw | A18 | A7 | LVCMOS33 | 最左侧数码管 |

#### 段选信号（低电平有效）
| 功能 | Verilog端口 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|-------------|----------|----------|--------|------|
| A段 | smg[0] | F15 | CA | LVCMOS33 | 上横段 |
| B段 | smg[1] | F13 | CB | LVCMOS33 | 右上竖段 |
| C段 | smg[2] | F14 | CC | LVCMOS33 | 右下竖段 |
| D段 | smg[3] | F16 | CD | LVCMOS33 | 下横段 |
| E段 | smg[4] | E17 | CE | LVCMOS33 | 左下竖段 |
| F段 | smg[5] | C14 | CF | LVCMOS33 | 左上竖段 |
| G段 | smg[6] | C15 | CG | LVCMOS33 | 中横段 |
| 小数点 | smg[7] | E13 | DP | LVCMOS33 | 小数点 |

### LED指示灯（高电平点亮）
#### 红色LED（异常指示）
| 功能 | Verilog端口 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|-------------|----------|----------|--------|------|
| 异常停止 | red | N19 | RLD0 | LVCMOS33 | 系统异常时点亮 |

#### 绿色LED（工作状态流水灯）
| 功能 | Verilog端口 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|-------------|----------|----------|--------|------|
| 流水灯0 | green[0] | J17 | GLD0 | LVCMOS33 | 工作进度指示 |
| 流水灯1 | green[1] | L14 | GLD1 | LVCMOS33 | 工作进度指示 |
| 流水灯2 | green[2] | L15 | GLD2 | LVCMOS33 | 工作进度指示 |
| 流水灯3 | green[3] | L16 | GLD3 | LVCMOS33 | 工作进度指示 |
| 流水灯4 | green[4] | K16 | GLD4 | LVCMOS33 | 工作进度指示 |
| 流水灯5 | green[5] | M15 | GLD5 | LVCMOS33 | 工作进度指示 |
| 流水灯6 | green[6] | M16 | GLD6 | LVCMOS33 | 工作进度指示 |
| 流水灯7 | green[7] | M17 | GLD7 | LVCMOS33 | 工作进度指示 |

#### 黄色LED（设置状态指示）
| 功能 | Verilog端口 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|-------------|----------|----------|--------|------|
| 设置状态0 | yellow[0] | A21 | YLD0 | LVCMOS33 | 设置第一参数 |
| 设置状态1 | yellow[1] | E22 | YLD1 | LVCMOS33 | 设置第二参数 |

## 键盘布局参考

```
矩阵键盘布局（4x4）：
     col[0] col[1] col[2] col[3]
      K4     J4     L3     K3
row[0] M2  [  1  ] [  2  ] [  3  ] [ 功能 ]
row[1] K6  [  4  ] [  5  ] [  6  ] [ 功能 ]
row[2] J6  [  7  ] [  8  ] [  9  ] [ 功能 ]
row[3] J5  [ 功能] [  0  ] [ 功能] [ 功能 ]
```

## 数码管显示格式

```
8位数码管显示布局：
[h_qw][h_bw][h_sw][h_gw][l_qw][l_bw][l_sw][l_gw]
  A7    A6    A5    A4    A3    A2    A1    A0
千位  百位  十位  个位  千位  百位  十位  个位
     高4位数据           低4位数据
```

## 使用的约束文件

1. **`constraints_final.xdc`** - 推荐使用的完整约束文件
2. **`constraints.xdc`** - 主约束文件（已更新）
3. **`constraints_fixed.xdc`** - 修正版约束文件（已更新）
4. **`constraints_minimal.xdc`** - 最简版约束文件（已更新）

## 验证方法

在Vivado中可以使用以下命令验证引脚分配：

```tcl
# 检查约束文件语法
check_syntax [get_files constraints_final.xdc]

# 查看IO报告
report_io -file io_report.txt

# 检查特定端口的约束
get_property PACKAGE_PIN [get_ports {col[0]}]
get_property IOSTANDARD [get_ports {col[0]}]
```

现在所有约束文件都使用正确的键盘引脚，应该不会再出现引脚分配错误了！
