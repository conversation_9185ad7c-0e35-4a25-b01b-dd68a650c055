# 药片装瓶系统快速测试步骤

## 快速验证流程（15分钟）

### 第一步：硬件连接验证（2分钟）
```
检查清单：
□ USB线连接FPGA板
□ 电源指示灯亮起
□ 矩阵键盘连接到KP1-KP8
□ 数码管模块连接正确
□ LED灯组连接正确
```

### 第二步：下载程序（3分钟）
1. 打开Vivado Hardware Manager
2. 连接目标设备
3. 下载比特流文件
4. 观察下载成功提示

### 第三步：基础功能快速测试（10分钟）

#### 3.1 复位测试（30秒）
**操作：** 按下复位键S6
**观察：** 
- 数码管显示 "00000000"
- 黄色LED两个都亮（状态11）
- 其他LED熄灭

#### 3.2 键盘输入测试（2分钟）
**操作：** 依次按下矩阵键盘 1, 2, 3
**观察：** 数码管显示 "00000123"

**如果键盘无响应，检查：**
- KP1-KP8引脚连接
- 矩阵键盘电源
- 按键是否按到底

#### 3.3 参数设置测试（3分钟）
**第一个参数（药瓶总数）：**
1. 按键盘输入：5
2. 按确认键S2
3. 观察：黄色LED变为"01"，数码管清零

**第二个参数（单瓶药片数）：**
1. 按键盘输入：10
2. 按确认键S2  
3. 观察：黄色LED变为"00"

#### 3.4 工作模式测试（4分钟）
**启动系统：**
1. 按开始键S1
2. 观察：绿色LED开始流水灯效果
3. 观察：数码管显示变化

**完成测试：**
- 等待系统完成（约40秒，因为设置了5瓶×10片）
- 观察：所有绿色LED同时亮起
- 观察：数码管显示最终状态

## 常见问题快速诊断

### 问题1：数码管不显示
**可能原因：** 段选或位选信号问题
**快速检查：**
```verilog
// 在代码中临时添加测试信号
assign smg = 8'b11000000; // 强制显示0
assign l_gw = 1'b0;       // 强制选中最右边数码管
```

### 问题2：按键无响应  
**可能原因：** 矩阵键盘连接或扫描问题
**快速检查：**
- 用万用表测试KP1-KP8引脚电平
- 检查键盘模块电源

### 问题3：LED不亮
**可能原因：** 极性问题或引脚错误
**快速检查：**
```verilog
// 临时强制LED亮起测试
assign red = 1'b1;     // 如果还不亮，说明是低电平触发
assign red = 1'b0;     // 应该亮起
```

### 问题4：系统工作异常
**可能原因：** 时钟或复位问题
**快速检查：**
- 确认时钟引脚Y18连接
- 确认复位按键S6功能正常

## 调试模式设置

如果需要快速调试，可以修改代码中的时间参数：

### 加速测试参数
在 `main.v` 文件第133行，将装瓶时间从1秒改为0.1秒：
```verilog
// 原代码：
if (cnt == 27'd100_000_000) begin  // 1秒装一片

// 修改为：
if (cnt == 27'd10_000_000) begin   // 0.1秒装一片（快速测试）
```

在 `light_control.v` 文件中，将流水灯速度加快：
```verilog
// 原代码：
if(cnt_1 == 26'd50_000_000) begin  // 0.5秒

// 修改为：
if(cnt_1 == 26'd5_000_000) begin   // 0.05秒（快速测试）
```

### 测试用小参数
建议使用以下参数进行快速测试：
- 药瓶总数：3
- 单瓶药片数：5
- 总时间：约15秒（使用加速参数）

## 完整测试验证表

| 测试项目     | 操作步骤       | 预期结果                     | ✓/✗ | 备注 |
| ------------ | -------------- | ---------------------------- | --- | ---- |
| **基础功能** |
| 复位功能     | 按S6复位键     | 数码管显示00000000，黄LED=11 |     |      |
| 时钟工作     | 观察数码管扫描 | 无闪烁，正常显示             |     |      |
| **键盘输入** |
| 数字键1      | 按矩阵键盘1    | 数码管显示00000001           |     |      |
| 数字键2      | 按矩阵键盘2    | 数码管显示00000002           |     |      |
| 多位输入     | 按1,2,3        | 数码管显示00000123           |     |      |
| 确认键       | 按S2确认键     | 黄LED状态改变                |     |      |
| **参数设置** |
| 第一参数     | 输入5，按S2    | 黄LED变为01                  |     |      |
| 第二参数     | 输入10，按S2   | 黄LED变为00                  |     |      |
| **工作模式** |
| 普通模式     | SW0=OFF，按S1  | 绿LED流水，连续工作          |     |      |
| 定制模式     | SW0=ON，按S1   | 每瓶后暂停设置               |     |      |
| **显示功能** |
| 显示切换     | 切换SW1        | 数码管内容改变               |     |      |
| 进度显示     | 工作过程中观察 | 实时显示当前状态             |     |      |
| **异常处理** |
| 参数超限     | 输入1000       | 红LED亮，系统停止            |     |      |
| 工作完成     | 等待完成       | 所有绿LED亮                  |     |      |

## 性能测试

### 时序测试
1. **按键响应时间**：应在20ms内响应
2. **数码管刷新率**：2ms扫描周期，无闪烁
3. **LED流水速度**：0.5秒移动一位

### 精度测试
1. **计数精度**：装瓶计数应准确无误
2. **时间精度**：每片装瓶时间应为1秒±10ms
3. **显示精度**：数码管显示应与内部计数一致

## 最终验收标准

系统通过测试的标准：
- [ ] 所有基础功能正常
- [ ] 键盘输入响应正确
- [ ] 参数设置功能完整
- [ ] 两种工作模式都能正常运行
- [ ] 异常处理机制有效
- [ ] 显示功能准确
- [ ] 长时间运行稳定

**注意：** 如果任何一项测试失败，请参考故障排除部分或检查硬件连接。


<function_calls>
<invoke name="view">
<parameter name="path">main.v
