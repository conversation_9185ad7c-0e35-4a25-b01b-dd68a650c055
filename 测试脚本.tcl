# 药片装瓶系统Vivado测试脚本
# 用于自动化编译、综合、实现和下载

# 设置项目路径和名称
set project_name "pill_bottling_system"
set project_dir "./vivado_project"
set part_name "xc7a100tfgg484-1"

# 创建项目
proc create_project_if_not_exists {} {
    global project_name project_dir part_name
    
    if {![file exists $project_dir]} {
        puts "创建新项目: $project_name"
        create_project $project_name $project_dir -part $part_name -force
        
        # 添加源文件
        add_files -norecurse {Top.v main.v keyboard.v nixie_tube.v light_control.v data_transform.v add.v}
        
        # 添加约束文件
        add_files -fileset constrs_1 -norecurse constraints.xdc
        
        # 设置顶层模块
        set_property top Top [current_fileset]
        
        puts "项目创建完成"
    } else {
        puts "打开现有项目: $project_name"
        open_project $project_dir/$project_name.xpr
    }
}

# 检查语法
proc check_syntax {} {
    puts "检查语法..."
    check_syntax -return_string
    puts "语法检查完成"
}

# 综合设计
proc run_synthesis {} {
    puts "开始综合..."
    reset_run synth_1
    launch_runs synth_1 -jobs 4
    wait_on_run synth_1
    
    if {[get_property PROGRESS [get_runs synth_1]] != "100%"} {
        error "综合失败"
    }
    puts "综合完成"
}

# 实现设计
proc run_implementation {} {
    puts "开始实现..."
    reset_run impl_1
    launch_runs impl_1 -jobs 4
    wait_on_run impl_1
    
    if {[get_property PROGRESS [get_runs impl_1]] != "100%"} {
        error "实现失败"
    }
    puts "实现完成"
}

# 生成比特流
proc generate_bitstream {} {
    puts "生成比特流..."
    launch_runs impl_1 -to_step write_bitstream -jobs 4
    wait_on_run impl_1
    
    if {[get_property PROGRESS [get_runs impl_1]] != "100%"} {
        error "比特流生成失败"
    }
    puts "比特流生成完成"
}

# 检查时序
proc check_timing {} {
    puts "检查时序..."
    open_run impl_1
    
    # 生成时序报告
    report_timing_summary -delay_type min_max -report_unconstrained -check_timing_verbose -max_paths 10 -input_pins -routable_nets -file timing_summary.rpt
    
    # 检查是否有时序违例
    set wns [get_property SLACK [get_timing_paths -max_paths 1 -nworst 1 -setup]]
    set whs [get_property SLACK [get_timing_paths -max_paths 1 -nworst 1 -hold]]
    
    puts "建立时间最差路径余量: $wns ns"
    puts "保持时间最差路径余量: $whs ns"
    
    if {$wns < 0} {
        puts "警告: 存在建立时间违例"
    }
    if {$whs < 0} {
        puts "警告: 存在保持时间违例"
    }
}

# 检查资源利用率
proc check_utilization {} {
    puts "检查资源利用率..."
    open_run impl_1
    
    report_utilization -file utilization.rpt
    
    # 获取关键资源利用率
    set lut_util [get_property USED [get_cells -hierarchical -filter {REF_NAME =~ LUT*}]]
    set ff_util [get_property USED [get_cells -hierarchical -filter {REF_NAME =~ FD*}]]
    
    puts "LUT使用数量: $lut_util"
    puts "触发器使用数量: $ff_util"
}

# 连接硬件并下载
proc program_device {} {
    puts "连接硬件..."
    
    # 打开硬件管理器
    open_hw_manager
    connect_hw_server -allow_non_jtag
    
    # 自动检测硬件目标
    open_hw_target
    
    # 获取FPGA设备
    set device [get_hw_devices]
    if {[llength $device] == 0} {
        error "未检测到FPGA设备"
    }
    
    current_hw_device [lindex $device 0]
    
    # 设置比特流文件
    set bitstream_file "$project_dir/$project_name.runs/impl_1/Top.bit"
    set_property PROGRAM.FILE $bitstream_file [current_hw_device]
    
    # 下载比特流
    puts "下载比特流到FPGA..."
    program_hw_devices [current_hw_device]
    
    puts "下载完成"
    close_hw_manager
}

# 完整构建流程
proc full_build {} {
    create_project_if_not_exists
    check_syntax
    run_synthesis
    run_implementation
    generate_bitstream
    check_timing
    check_utilization
}

# 快速测试流程（仅编译检查）
proc quick_test {} {
    create_project_if_not_exists
    check_syntax
    run_synthesis
    puts "快速测试完成 - 语法和综合通过"
}

# 主函数
proc main {args} {
    if {[llength $args] == 0} {
        puts "用法: vivado -mode tcl -source 测试脚本.tcl -tclargs <command>"
        puts "可用命令:"
        puts "  full_build    - 完整构建流程"
        puts "  quick_test    - 快速测试（语法+综合）"
        puts "  program       - 仅下载到FPGA"
        puts "  timing        - 检查时序"
        puts "  utilization   - 检查资源利用率"
        return
    }
    
    set command [lindex $args 0]
    
    switch $command {
        "full_build" {
            full_build
        }
        "quick_test" {
            quick_test
        }
        "program" {
            program_device
        }
        "timing" {
            create_project_if_not_exists
            check_timing
        }
        "utilization" {
            create_project_if_not_exists
            check_utilization
        }
        default {
            puts "未知命令: $command"
        }
    }
}

# 如果直接运行脚本，执行主函数
if {[info exists argv]} {
    main $argv
}

# 使用示例:
# vivado -mode tcl -source 测试脚本.tcl -tclargs full_build
# vivado -mode tcl -source 测试脚本.tcl -tclargs quick_test
# vivado -mode tcl -source 测试脚本.tcl -tclargs program
