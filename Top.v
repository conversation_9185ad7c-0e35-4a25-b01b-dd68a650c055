// LED测试顶层模块
// 用于诊断LED问题的简化版本

`timescale 1ns / 1ps

module Top(
    input                  sys_clk,
    input                  sys_rst_n,
    input                  start,           // 未使用，但保持接口一致
    input                  pil_mode,        // 未使用
    input                  ack,             // 未使用
    input                  display_mode,    // 未使用
    
    input      wire[3:0]   col,             // 未使用
    output     wire[3:0]   row,             // 未使用
    
    // 数码管接口
    output     wire        l_qw,
    output     wire        l_bw,
    output     wire        l_sw,
    output     wire        l_gw,
    output     wire        h_qw,
    output     wire        h_bw,
    output     wire        h_sw,
    output     wire        h_gw,
    output     wire[7:0]   smg,
    
    // LED接口
    output     wire        red,
    output     wire[7:0]   green,
    output     wire[1:0]   yellow
);

// 未使用的输出设为默认值
assign row = 4'b1111;

// 实例化LED测试模块
LED_test led_test_inst(
    .sys_clk        (sys_clk),
    .sys_rst_n      (sys_rst_n),
    
    // LED输出
    .red            (red),
    .green          (green),
    .yellow         (yellow),
    
    // 数码管输出
    .smg            (smg),
    .l_gw           (l_gw),
    .l_sw           (l_sw),
    .l_bw           (l_bw),
    .l_qw           (l_qw),
    .h_gw           (h_gw),
    .h_sw           (h_sw),
    .h_bw           (h_bw),
    .h_qw           (h_qw)
);

endmodule
