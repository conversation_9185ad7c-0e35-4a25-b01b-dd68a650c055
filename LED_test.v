// LED测试模块 - 用于诊断LED问题
// 此模块将循环测试所有LED，帮助确定LED的极性和位置

`timescale 1ns / 1ps

module LED_test(
    input                  sys_clk,
    input                  sys_rst_n,
    
    // LED输出
    output reg             red,
    output reg [7:0]       green,
    output reg [1:0]       yellow,
    
    // 数码管输出（用于显示测试状态）
    output reg [7:0]       smg,
    output reg             l_gw,
    output reg             l_sw,
    output reg             l_bw,
    output reg             l_qw,
    output reg             h_gw,
    output reg             h_sw,
    output reg             h_bw,
    output reg             h_qw
);

// 计数器和状态
reg [27:0] counter;
reg [3:0] test_state;

// 测试周期：1秒 = 100,000,000个时钟周期
parameter TEST_PERIOD = 28'd100_000_000;

// 数码管显示数字的编码（共阴极，低电平点亮）
parameter NUM_0 = 8'b11000000;  // 显示0
parameter NUM_1 = 8'b11111001;  // 显示1
parameter NUM_2 = 8'b10100100;  // 显示2
parameter NUM_3 = 8'b10110000;  // 显示3
parameter NUM_4 = 8'b10011001;  // 显示4
parameter NUM_5 = 8'b10010010;  // 显示5
parameter NUM_6 = 8'b10000010;  // 显示6
parameter NUM_7 = 8'b11111000;  // 显示7
parameter NUM_8 = 8'b10000000;  // 显示8
parameter NUM_9 = 8'b10010000;  // 显示9
parameter NUM_A = 8'b10001000;  // 显示A
parameter NUM_B = 8'b10000011;  // 显示b
parameter NUM_C = 8'b11000110;  // 显示C
parameter NUM_D = 8'b10100001;  // 显示d
parameter NUM_E = 8'b10000110;  // 显示E
parameter NUM_F = 8'b10001110;  // 显示F

// 主控制逻辑
always @(posedge sys_clk or negedge sys_rst_n) begin
    if (!sys_rst_n) begin
        counter <= 28'd0;
        test_state <= 4'd0;
        red <= 1'b0;
        green <= 8'b00000000;
        yellow <= 2'b00;
        
        // 数码管初始化
        smg <= NUM_0;
        l_gw <= 1'b0;    // 点亮个位
        l_sw <= 1'b1;
        l_bw <= 1'b1;
        l_qw <= 1'b1;
        h_gw <= 1'b1;
        h_sw <= 1'b1;
        h_bw <= 1'b1;
        h_qw <= 1'b1;
    end
    else begin
        // 计数器递增
        if (counter >= TEST_PERIOD) begin
            counter <= 28'd0;
            test_state <= test_state + 1'b1;
        end
        else begin
            counter <= counter + 1'b1;
        end
        
        // 根据测试状态控制LED
        case (test_state)
            4'd0: begin  // 测试状态0：全部熄灭
                red <= 1'b0;
                green <= 8'b00000000;
                yellow <= 2'b00;
                smg <= NUM_0;
                l_gw <= 1'b0;    // 显示0
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
            
            4'd1: begin  // 测试状态1：红色LED
                red <= 1'b1;
                green <= 8'b00000000;
                yellow <= 2'b00;
                smg <= NUM_1;
                l_gw <= 1'b0;    // 显示1
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
            
            4'd2: begin  // 测试状态2：绿色LED全亮
                red <= 1'b0;
                green <= 8'b11111111;
                yellow <= 2'b00;
                smg <= NUM_2;
                l_gw <= 1'b0;    // 显示2
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
            
            4'd3: begin  // 测试状态3：绿色LED流水灯
                red <= 1'b0;
                // 根据计数器低位产生流水灯效果
                case (counter[25:23])
                    3'd0: green <= 8'b00000001;
                    3'd1: green <= 8'b00000010;
                    3'd2: green <= 8'b00000100;
                    3'd3: green <= 8'b00001000;
                    3'd4: green <= 8'b00010000;
                    3'd5: green <= 8'b00100000;
                    3'd6: green <= 8'b01000000;
                    3'd7: green <= 8'b10000000;
                endcase
                yellow <= 2'b00;
                smg <= NUM_3;
                l_gw <= 1'b0;    // 显示3
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
            
            4'd4: begin  // 测试状态4：黄色LED[0]
                red <= 1'b0;
                green <= 8'b00000000;
                yellow <= 2'b01;
                smg <= NUM_4;
                l_gw <= 1'b0;    // 显示4
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
            
            4'd5: begin  // 测试状态5：黄色LED[1]
                red <= 1'b0;
                green <= 8'b00000000;
                yellow <= 2'b10;
                smg <= NUM_5;
                l_gw <= 1'b0;    // 显示5
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
            
            4'd6: begin  // 测试状态6：黄色LED全亮
                red <= 1'b0;
                green <= 8'b00000000;
                yellow <= 2'b11;
                smg <= NUM_6;
                l_gw <= 1'b0;    // 显示6
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
            
            4'd7: begin  // 测试状态7：所有LED全亮
                red <= 1'b1;
                green <= 8'b11111111;
                yellow <= 2'b11;
                smg <= NUM_7;
                l_gw <= 1'b0;    // 显示7
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
            
            4'd8: begin  // 测试状态8：绿色LED奇数位
                red <= 1'b0;
                green <= 8'b10101010;
                yellow <= 2'b00;
                smg <= NUM_8;
                l_gw <= 1'b0;    // 显示8
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
            
            4'd9: begin  // 测试状态9：绿色LED偶数位
                red <= 1'b0;
                green <= 8'b01010101;
                yellow <= 2'b00;
                smg <= NUM_9;
                l_gw <= 1'b0;    // 显示9
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
            
            default: begin  // 其他状态：闪烁所有LED
                red <= counter[24];
                green <= {8{counter[24]}};
                yellow <= {2{counter[24]}};
                smg <= NUM_F;
                l_gw <= 1'b0;    // 显示F
                l_sw <= 1'b1;
                l_bw <= 1'b1;
                l_qw <= 1'b1;
                h_gw <= 1'b1;
                h_sw <= 1'b1;
                h_bw <= 1'b1;
                h_qw <= 1'b1;
            end
        endcase
    end
end

endmodule
