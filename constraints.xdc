# 药片装瓶系统约束文件
# 基于 Xilinx xc7a100tfgg484-1 FPGA
# 适用于 BUPT Digital Logic 药片装瓶项目

#######################################
# 时钟约束
#######################################
# 100MHz 系统时钟
set_property PACKAGE_PIN Y18 [get_ports sys_clk]
set_property IOSTANDARD LVCMOS33 [get_ports sys_clk]
create_clock -period 10.000 -name sys_clk_pin -waveform {0.000 5.000} -add [get_ports sys_clk]

#######################################
# 复位信号
#######################################
# 使用按键S6作为复位信号（低电平有效）
set_property PACKAGE_PIN P20 [get_ports sys_rst_n]
set_property IOSTANDARD LVCMOS33 [get_ports sys_rst_n]

#######################################
# 控制输入信号
#######################################
# 开始信号 - 使用按键S1
set_property PACKAGE_PIN R1 [get_ports start]
set_property IOSTANDARD LVCMOS33 [get_ports start]

# 药片模式选择 - 使用拨码开关SW0
set_property PACKAGE_PIN W4 [get_ports pil_mode]
set_property IOSTANDARD LVCMOS33 [get_ports pil_mode]

# 确认信号 - 使用按键S2
set_property PACKAGE_PIN P1 [get_ports ack]
set_property IOSTANDARD LVCMOS33 [get_ports ack]

# 显示模式控制 - 使用拨码开关SW1
set_property PACKAGE_PIN R4 [get_ports display_mode]
set_property IOSTANDARD LVCMOS33 [get_ports display_mode]

#######################################
# 4x4矩阵键盘接口
#######################################
# 列输入信号 col[3:0]
set_property IOSTANDARD LVCMOS33 [get_ports {col[*]}]

# 行输出信号 row[3:0]
set_property IOSTANDARD LVCMOS33 [get_ports {row[*]}]

#######################################
# 数码管显示接口
#######################################
# 位选信号（低电平有效）
# 低4位数码管位选

# 高4位数码管位选

set_property IOSTANDARD LVCMOS33 [get_ports l_gw]
set_property IOSTANDARD LVCMOS33 [get_ports l_sw]
set_property IOSTANDARD LVCMOS33 [get_ports l_bw]
set_property IOSTANDARD LVCMOS33 [get_ports l_qw]
set_property IOSTANDARD LVCMOS33 [get_ports h_gw]
set_property IOSTANDARD LVCMOS33 [get_ports h_sw]
set_property IOSTANDARD LVCMOS33 [get_ports h_bw]
set_property IOSTANDARD LVCMOS33 [get_ports h_qw]

# 段选信号（低电平有效）

set_property IOSTANDARD LVCMOS33 [get_ports {smg[*]}]

#######################################
# LED指示灯接口
#######################################
# 红色LED - 异常停止指示（使用1个红色LED）
set_property PACKAGE_PIN N19 [get_ports red]
set_property IOSTANDARD LVCMOS33 [get_ports red]

# 绿色LED - 工作状态流水灯（8个绿色LED）

set_property IOSTANDARD LVCMOS33 [get_ports {green[*]}]

# 黄色LED - 设置状态指示（使用2个黄色LED）

set_property IOSTANDARD LVCMOS33 [get_ports {yellow[*]}]

#######################################
# 时序约束
#######################################
# 设置输入延迟
set_input_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports {col[*]}]
set_input_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports {col[*]}]
set_input_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports start]
set_input_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports start]
set_input_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports ack]
set_input_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports ack]
set_input_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports pil_mode]
set_input_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports pil_mode]
set_input_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports display_mode]
set_input_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports display_mode]

# 设置输出延迟
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports {row[*]}]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports {row[*]}]
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports {smg[*]}]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports {smg[*]}]
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports {green[*]}]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports {green[*]}]
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports {yellow[*]}]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports {yellow[*]}]
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports red]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports red]

# 数码管位选信号输出延迟
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports l_gw]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports l_gw]
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports l_sw]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports l_sw]
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports l_bw]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports l_bw]
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports l_qw]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports l_qw]
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports h_gw]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports h_gw]
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports h_sw]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports h_sw]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports h_bw]
set_output_delay -clock [get_clocks sys_clk_pin] -min -add_delay 2.000 [get_ports h_qw]
set_output_delay -clock [get_clocks sys_clk_pin] -max -add_delay 5.000 [get_ports h_qw]

#######################################
# 配置属性
#######################################
# 配置未使用的引脚为上拉
set_property BITSTREAM.CONFIG.UNUSEDPIN PULLUP [current_design]

# 配置比特流压缩
set_property BITSTREAM.GENERAL.COMPRESS TRUE [current_design]

#######################################
# 引脚分配说明
#######################################
#
# 系统功能映射：
# 1. 时钟：Y18 (100MHz)
# 2. 复位：P20 (按键S6，低电平有效)
# 3. 控制信号：
#    - start: R1 (按键S1)
#    - ack: P1 (按键S2)
#    - pil_mode: W4 (拨码开关SW0)
#    - display_mode: R4 (拨码开关SW1)
# 4. 矩阵键盘：KP1-KP8 (4x4键盘)
# 5. 数码管：A0-A7位选，CA-CG/DP段选
# 6. LED指示：
#    - 红灯：异常停止
#    - 绿灯：工作状态流水灯
#    - 黄灯：设置状态指示
#
# 注意事项：
# 1. 数码管和LED都是低电平触发
# 2. 按键需要防抖处理（代码中已实现）
# 3. 矩阵键盘采用行扫描方式
# 4. 系统时钟为100MHz，周期10ns

set_property PACKAGE_PIN M17 [get_ports {green[7]}]
set_property PACKAGE_PIN M16 [get_ports {green[6]}]
set_property PACKAGE_PIN M15 [get_ports {green[5]}]
set_property PACKAGE_PIN K16 [get_ports {green[4]}]
set_property PACKAGE_PIN L16 [get_ports {green[3]}]
set_property PACKAGE_PIN L15 [get_ports {green[2]}]
set_property PACKAGE_PIN L14 [get_ports {green[1]}]
set_property PACKAGE_PIN J17 [get_ports {green[0]}]
set_property PACKAGE_PIN J5 [get_ports {row[3]}]
set_property PACKAGE_PIN J6 [get_ports {row[2]}]
set_property PACKAGE_PIN K6 [get_ports {row[1]}]
set_property PACKAGE_PIN M2 [get_ports {row[0]}]
set_property PACKAGE_PIN K3 [get_ports {col[3]}]
set_property PACKAGE_PIN L3 [get_ports {col[2]}]
set_property PACKAGE_PIN J4 [get_ports {col[1]}]
set_property PACKAGE_PIN K4 [get_ports {col[0]}]
set_property PACKAGE_PIN E13 [get_ports {smg[7]}]
set_property PACKAGE_PIN C15 [get_ports {smg[6]}]
set_property PACKAGE_PIN C14 [get_ports {smg[5]}]
set_property PACKAGE_PIN E17 [get_ports {smg[4]}]
set_property PACKAGE_PIN F16 [get_ports {smg[3]}]
set_property PACKAGE_PIN F14 [get_ports {smg[2]}]
set_property PACKAGE_PIN F13 [get_ports {smg[1]}]
set_property PACKAGE_PIN F15 [get_ports {smg[0]}]
set_property PACKAGE_PIN A21 [get_ports {yellow[1]}]
set_property PACKAGE_PIN E22 [get_ports {yellow[0]}]
set_property PACKAGE_PIN A20 [get_ports h_bw]
set_property PACKAGE_PIN E18 [get_ports h_gw]
set_property PACKAGE_PIN A18 [get_ports h_qw]
set_property PACKAGE_PIN B20 [get_ports h_sw]
set_property PACKAGE_PIN D19 [get_ports l_bw]
set_property PACKAGE_PIN C19 [get_ports l_gw]
set_property PACKAGE_PIN F18 [get_ports l_qw]
set_property PACKAGE_PIN E19 [get_ports l_sw]
