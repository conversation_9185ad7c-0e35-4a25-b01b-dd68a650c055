# 药片装瓶系统完整指南

## 项目概述

### 基本信息
- **项目名称**：智能药片装瓶控制系统
- **开发平台**：FPGA Minisys实验板
- **目标芯片**：Xilinx xc7a100tfgg484-1
- **开发语言**：Verilog HDL
- **设计工具**：Vivado 2018.3+
- **课程背景**：北京邮电大学数字逻辑课程设计

### 系统功能概述
本系统是一个基于FPGA的智能药片装瓶控制系统，能够自动控制药片装瓶过程，支持用户自定义参数设置，具备实时显示、状态指示和异常处理功能。

## 系统架构与功能

### 🏗️ 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        Top Module                          │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│  keyboard   │    main     │data_transform│  nixie_tube    │
│   模块      │   主控模块   │  数据转换模块 │   数码管模块    │
├─────────────┴─────────────┴─────────────┴─────────────────┤
│                   light_control                           │
│                    灯光控制模块                             │
└─────────────────────────────────────────────────────────────┘
```

### 📋 核心功能模块详解

#### 1. 主控制模块 (main.v)
**核心功能：**
- **参数管理**：
  - 药瓶总数设置 (max_bot_num): 0-999
  - 单瓶药片数设置 (max_sgl_bot): 0-999
  - 当前瓶内药片计数 (now_bot_bil_num)
  - 已完成瓶数统计 (bot_finished)

- **工作模式**：
  - 普通模式 (ordinary): 连续装瓶，使用统一参数
  - 定制模式 (customization): 每瓶完成后可重新设置参数

- **状态控制**：
  - 设置阶段 (setting): 参数配置状态
  - 工作阶段 (working): 装瓶执行状态

- **时序控制**：
  - 装瓶速度: 1片/秒 (基于100MHz时钟)
  - 防抖处理: 20ms防抖时间

#### 2. 键盘输入模块 (keyboard.v)
**扫描机制：**
- 4×4矩阵键盘行扫描
- 扫描周期: 20ms
- 防抖时间: 20ms

**键盘布局：**
```
┌─────┬─────┬─────┬─────┐
│  1  │  2  │  3  │ 功能 │
├─────┼─────┼─────┼─────┤
│  4  │  5  │  6  │ 功能 │
├─────┼─────┼─────┼─────┤
│  7  │  8  │  9  │ 功能 │
├─────┼─────┼─────┼─────┤
│ 功能 │  0  │ 功能 │ 功能 │
└─────┴─────┴─────┴─────┘
```

**输入处理：**
- 支持0-9数字输入
- 最大输入值: 999
- 累积输入: temp_data = temp_data × 10 + 当前按键
- 确认清零: 通过ack信号清零重新输入

#### 3. 数据转换模块 (data_transform.v)
**显示数据选择：**
- **设置模式**: 显示键盘输入的临时数据
- **工作模式**:
  - 设置显示 (display_mode=0): 显示配置参数
    - 格式: 药瓶总数(4位) + 单瓶药片数(4位)
  - 工作显示 (display_mode=1): 显示实时状态
    - 格式: 已完成瓶数(4位) + 当前瓶内药片数(4位)

#### 4. 数码管显示模块 (nixie_tube.v)
**显示特性：**
- 8位七段数码管
- 动态扫描显示，4ms刷新周期
- 支持显示范围: 00000000-99999999

**控制信号：**
- 位选信号: A7-A0 (低电平有效)
- 段选信号: CA-CG + DP (低电平有效)

**显示格式：**
```
[千万][百万][十万][万][千][百][十][个]
  A7    A6    A5   A4  A3  A2  A1  A0
```

#### 5. 灯光控制模块 (light_control.v)
**LED指示功能：**
- **红色LED**: 异常停止指示
- **绿色LED流水灯**: 工作进度指示
  - 8个LED，0.5秒移动一位
  - 完成时全部点亮
- **黄色LED**: 设置状态指示
  - 11: 设置药瓶总数阶段
  - 01: 设置单瓶药片数阶段
  - 00: 设置完成，准备工作

## 硬件资源配置

### 🔌 完整引脚分配表

#### 系统基础信号
| 功能 | 端口名 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|--------|----------|----------|--------|------|
| 系统时钟 | sys_clk | Y18 | 100MHz | LVCMOS33 | 主时钟源 |
| 系统复位 | sys_rst_n | P20 | S6 | LVCMOS33 | 低电平有效复位 |
| 开始信号 | start | R1 | S1 | LVCMOS33 | 启动装瓶过程 |
| 确认信号 | ack | P1 | S2 | LVCMOS33 | 确认键盘输入 |
| 药片模式 | pil_mode | W4 | SW0 | LVCMOS33 | 普通/定制模式选择 |
| 显示模式 | display_mode | R4 | SW1 | LVCMOS33 | 显示内容切换 |

#### 4×4矩阵键盘接口
| 功能 | 端口名 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|--------|----------|----------|--------|------|
| 键盘列0 | col[0] | K4 | K1 | LVCMOS33 | 键盘列输入信号 |
| 键盘列1 | col[1] | J4 | K2 | LVCMOS33 | 键盘列输入信号 |
| 键盘列2 | col[2] | L3 | K3 | LVCMOS33 | 键盘列输入信号 |
| 键盘列3 | col[3] | K3 | K4 | LVCMOS33 | 键盘列输入信号 |
| 键盘行0 | row[0] | M2 | K5 | LVCMOS33 | 键盘行输出信号 |
| 键盘行1 | row[1] | K6 | K6 | LVCMOS33 | 键盘行输出信号 |
| 键盘行2 | row[2] | J6 | K7 | LVCMOS33 | 键盘行输出信号 |
| 键盘行3 | row[3] | J5 | K8 | LVCMOS33 | 键盘行输出信号 |

#### 8位数码管显示接口
**位选信号 (低电平有效)**
| 功能 | 端口名 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|--------|----------|----------|--------|------|
| 个位选择 | l_gw | C19 | A0 | LVCMOS33 | 最右侧数码管 |
| 十位选择 | l_sw | E19 | A1 | LVCMOS33 | 第2位数码管 |
| 百位选择 | l_bw | D19 | A2 | LVCMOS33 | 第3位数码管 |
| 千位选择 | l_qw | F18 | A3 | LVCMOS33 | 第4位数码管 |
| 万位选择 | h_gw | E18 | A4 | LVCMOS33 | 第5位数码管 |
| 十万位选择 | h_sw | B20 | A5 | LVCMOS33 | 第6位数码管 |
| 百万位选择 | h_bw | A20 | A6 | LVCMOS33 | 第7位数码管 |
| 千万位选择 | h_qw | A18 | A7 | LVCMOS33 | 最左侧数码管 |

**段选信号 (低电平有效)**
| 功能 | 端口名 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|--------|----------|----------|--------|------|
| A段 | smg[0] | F15 | CA | LVCMOS33 | 上横段 |
| B段 | smg[1] | F13 | CB | LVCMOS33 | 右上竖段 |
| C段 | smg[2] | F14 | CC | LVCMOS33 | 右下竖段 |
| D段 | smg[3] | F16 | CD | LVCMOS33 | 下横段 |
| E段 | smg[4] | E17 | CE | LVCMOS33 | 左下竖段 |
| F段 | smg[5] | C14 | CF | LVCMOS33 | 左上竖段 |
| G段 | smg[6] | C15 | CG | LVCMOS33 | 中横段 |
| 小数点 | smg[7] | E13 | DP | LVCMOS33 | 小数点 |

#### LED指示灯接口 (高电平点亮)
**红色LED (异常指示)**
| 功能 | 端口名 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|--------|----------|----------|--------|------|
| 异常停止 | red | N19 | RLD0 | LVCMOS33 | 系统异常时点亮 |

**绿色LED (工作状态流水灯)**
| 功能 | 端口名 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|--------|----------|----------|--------|------|
| 流水灯0 | green[0] | J17 | GLD0 | LVCMOS33 | 工作进度指示 |
| 流水灯1 | green[1] | L14 | GLD1 | LVCMOS33 | 工作进度指示 |
| 流水灯2 | green[2] | L15 | GLD2 | LVCMOS33 | 工作进度指示 |
| 流水灯3 | green[3] | L16 | GLD3 | LVCMOS33 | 工作进度指示 |
| 流水灯4 | green[4] | K16 | GLD4 | LVCMOS33 | 工作进度指示 |
| 流水灯5 | green[5] | M15 | GLD5 | LVCMOS33 | 工作进度指示 |
| 流水灯6 | green[6] | M16 | GLD6 | LVCMOS33 | 工作进度指示 |
| 流水灯7 | green[7] | M17 | GLD7 | LVCMOS33 | 工作进度指示 |

**黄色LED (设置状态指示)**
| 功能 | 端口名 | FPGA引脚 | 硬件标识 | IO标准 | 说明 |
|------|--------|----------|----------|--------|------|
| 设置状态0 | yellow[0] | A21 | YLD0 | LVCMOS33 | 设置第一参数指示 |
| 设置状态1 | yellow[1] | E22 | YLD1 | LVCMOS33 | 设置第二参数指示 |
