set_property PACKAGE_PIN Y18 [get_ports sys_clk]
set_property IOSTANDARD LVCMOS33 [get_ports sys_clk]
set_property PACKAGE_PIN P20 [get_ports sys_rst_n]
set_property IOSTANDARD LVCMOS33 [get_ports sys_rst_n]
set_property PACKAGE_PIN R1 [get_ports start]
set_property IOSTANDARD LVCMOS33 [get_ports start]
set_property PACKAGE_PIN P1 [get_ports ack]
set_property IOSTANDARD LVCMOS33 [get_ports ack]
set_property PACKAGE_PIN R4 [get_ports display_mode]
set_property IOSTANDARD LVCMOS33 [get_ports display_mode]
set_property IOSTANDARD LVCMOS33 [get_ports pil_mode]
set_property PACKAGE_PIN K3 [get_ports {col[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {col[3]}]
set_property PACKAGE_PIN L3 [get_ports {col[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {col[2]}]
set_property PACKAGE_PIN J4 [get_ports {col[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {col[1]}]
set_property PACKAGE_PIN K4 [get_ports {col[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {col[0]}]
set_property PACKAGE_PIN J5 [get_ports {row[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {row[3]}]
set_property PACKAGE_PIN J6 [get_ports {row[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {row[2]}]
set_property PACKAGE_PIN K6 [get_ports {row[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {row[1]}]
set_property PACKAGE_PIN M2 [get_ports {row[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {row[0]}]
set_property PACKAGE_PIN C19 [get_ports l_gw]
set_property IOSTANDARD LVCMOS33 [get_ports l_gw]
set_property PACKAGE_PIN E19 [get_ports l_sw]
set_property PACKAGE_PIN A16 [get_ports pil_mode]
set_property IOSTANDARD LVCMOS33 [get_ports l_sw]
set_property PACKAGE_PIN D19 [get_ports l_bw]
set_property IOSTANDARD LVCMOS33 [get_ports l_bw]
set_property PACKAGE_PIN F18 [get_ports l_qw]
set_property IOSTANDARD LVCMOS33 [get_ports l_qw]
set_property PACKAGE_PIN E18 [get_ports h_gw]
set_property IOSTANDARD LVCMOS33 [get_ports h_gw]
set_property PACKAGE_PIN B20 [get_ports h_sw]
set_property IOSTANDARD LVCMOS33 [get_ports h_sw]
set_property PACKAGE_PIN A20 [get_ports h_bw]
set_property IOSTANDARD LVCMOS33 [get_ports h_bw]
set_property PACKAGE_PIN A18 [get_ports h_qw]
set_property IOSTANDARD LVCMOS33 [get_ports h_qw]
set_property PACKAGE_PIN F15 [get_ports {smg[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {smg[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {smg[6]}]
set_property IOSTANDARD LVCMOS33 [get_ports {smg[5]}]
set_property IOSTANDARD LVCMOS33 [get_ports {smg[4]}]
set_property IOSTANDARD LVCMOS33 [get_ports {smg[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {smg[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {smg[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {smg[0]}]
set_property PACKAGE_PIN F13 [get_ports {smg[1]}]
set_property PACKAGE_PIN F14 [get_ports {smg[2]}]
set_property PACKAGE_PIN F16 [get_ports {smg[3]}]
set_property PACKAGE_PIN E17 [get_ports {smg[4]}]
set_property PACKAGE_PIN C14 [get_ports {smg[5]}]
set_property PACKAGE_PIN C15 [get_ports {smg[6]}]
set_property PACKAGE_PIN E13 [get_ports {smg[7]}]
set_property PACKAGE_PIN N19 [get_ports red]
set_property IOSTANDARD LVCMOS33 [get_ports red]
set_property PACKAGE_PIN J17 [get_ports {green[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[6]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[5]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[4]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[0]}]
set_property PACKAGE_PIN L14 [get_ports {green[1]}]
set_property PACKAGE_PIN L15 [get_ports {green[2]}]
set_property PACKAGE_PIN L16 [get_ports {green[3]}]
set_property PACKAGE_PIN K16 [get_ports {green[4]}]
set_property PACKAGE_PIN M15 [get_ports {green[5]}]
set_property PACKAGE_PIN M16 [get_ports {green[6]}]
set_property PACKAGE_PIN M17 [get_ports {green[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {yellow[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {yellow[0]}]
set_property PACKAGE_PIN A21 [get_ports {yellow[0]}]
set_property PACKAGE_PIN E22 [get_ports {yellow[1]}]
