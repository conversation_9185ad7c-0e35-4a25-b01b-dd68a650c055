# 药片装瓶系统问题分析和解决方案

## 🔍 问题现象
- 按下S6按钮后数码管全为0 ✅ (正常)
- 最右边两个黄色LED为11 ✅ (正常，表示设置状态)
- 按其他按钮都没有反应 ❌ (异常)

## 🚨 发现的问题

### 1. 约束文件中的严重错误

#### 问题1：pil_mode引脚分配错误
```tcl
# 错误的约束 (管教.md第11行和第31行)
set_property IOSTANDARD LVCMOS33 [get_ports pil_mode]  # 第11行，没有PACKAGE_PIN
set_property PACKAGE_PIN A16 [get_ports pil_mode]      # 第31行，引脚错误

# 正确的约束应该是：
set_property PACKAGE_PIN W4 [get_ports pil_mode]
set_property IOSTANDARD LVCMOS33 [get_ports pil_mode]
```

#### 问题2：键盘引脚分配与参考不一致
您的约束文件中键盘引脚：
```tcl
# 您的约束 (col和row)
col[0] -> K4, col[1] -> J4, col[2] -> L3, col[3] -> K3
row[0] -> M2, row[1] -> K6, row[2] -> J6, row[3] -> J5
```

参考约束文件中的键盘引脚：
```tcl
# 参考约束 (keyboard_col_n和keyboard_row_n)
keyboard_col_n[0] -> L5, keyboard_col_n[1] -> J6, keyboard_col_n[2] -> K6, keyboard_col_n[3] -> M2
keyboard_row_n[0] -> K3, keyboard_row_n[1] -> L3, keyboard_row_n[2] -> J4, keyboard_row_n[3] -> K4
```

**发现：您的col和row引脚与参考文件的row和col引脚正好相反！**

### 2. 代码逻辑问题

#### 问题1：键盘扫描逻辑可能不匹配硬件
如果硬件连接与代码中的扫描逻辑不匹配，会导致按键无响应。

#### 问题2：复位逻辑
需要确认复位信号的极性是否正确。

## 🔧 解决方案

### 方案1：修正约束文件（推荐）

创建正确的约束文件：

```tcl
# 基础信号
set_property PACKAGE_PIN Y18 [get_ports sys_clk]
set_property IOSTANDARD LVCMOS33 [get_ports sys_clk]
create_clock -add -name sys_clk_pin -period 10.00 [get_ports sys_clk]

set_property PACKAGE_PIN P20 [get_ports sys_rst_n]
set_property IOSTANDARD LVCMOS33 [get_ports sys_rst_n]

set_property PACKAGE_PIN R1 [get_ports start]
set_property IOSTANDARD LVCMOS33 [get_ports start]

set_property PACKAGE_PIN P1 [get_ports ack]
set_property IOSTANDARD LVCMOS33 [get_ports ack]

# 修正pil_mode引脚
set_property PACKAGE_PIN W4 [get_ports pil_mode]
set_property IOSTANDARD LVCMOS33 [get_ports pil_mode]

set_property PACKAGE_PIN R4 [get_ports display_mode]
set_property IOSTANDARD LVCMOS33 [get_ports display_mode]

# 修正键盘引脚 - 根据参考约束调整
set_property PACKAGE_PIN L5 [get_ports {col[0]}]
set_property PACKAGE_PIN J6 [get_ports {col[1]}]
set_property PACKAGE_PIN K6 [get_ports {col[2]}]
set_property PACKAGE_PIN M2 [get_ports {col[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {col[*]}]

set_property PACKAGE_PIN K3 [get_ports {row[0]}]
set_property PACKAGE_PIN L3 [get_ports {row[1]}]
set_property PACKAGE_PIN J4 [get_ports {row[2]}]
set_property PACKAGE_PIN K4 [get_ports {row[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {row[*]}]

# 数码管约束 (保持不变)
set_property PACKAGE_PIN C19 [get_ports l_gw]
set_property PACKAGE_PIN E19 [get_ports l_sw]
set_property PACKAGE_PIN D19 [get_ports l_bw]
set_property PACKAGE_PIN F18 [get_ports l_qw]
set_property PACKAGE_PIN E18 [get_ports h_gw]
set_property PACKAGE_PIN B20 [get_ports h_sw]
set_property PACKAGE_PIN A20 [get_ports h_bw]
set_property PACKAGE_PIN A18 [get_ports h_qw]
set_property IOSTANDARD LVCMOS33 [get_ports l_gw]
set_property IOSTANDARD LVCMOS33 [get_ports l_sw]
set_property IOSTANDARD LVCMOS33 [get_ports l_bw]
set_property IOSTANDARD LVCMOS33 [get_ports l_qw]
set_property IOSTANDARD LVCMOS33 [get_ports h_gw]
set_property IOSTANDARD LVCMOS33 [get_ports h_sw]
set_property IOSTANDARD LVCMOS33 [get_ports h_bw]
set_property IOSTANDARD LVCMOS33 [get_ports h_qw]

# 段选信号
set_property PACKAGE_PIN F15 [get_ports {smg[0]}]
set_property PACKAGE_PIN F13 [get_ports {smg[1]}]
set_property PACKAGE_PIN F14 [get_ports {smg[2]}]
set_property PACKAGE_PIN F16 [get_ports {smg[3]}]
set_property PACKAGE_PIN E17 [get_ports {smg[4]}]
set_property PACKAGE_PIN C14 [get_ports {smg[5]}]
set_property PACKAGE_PIN C15 [get_ports {smg[6]}]
set_property PACKAGE_PIN E13 [get_ports {smg[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {smg[*]}]

# LED约束
set_property PACKAGE_PIN N19 [get_ports red]
set_property IOSTANDARD LVCMOS33 [get_ports red]

set_property PACKAGE_PIN J17 [get_ports {green[0]}]
set_property PACKAGE_PIN L14 [get_ports {green[1]}]
set_property PACKAGE_PIN L15 [get_ports {green[2]}]
set_property PACKAGE_PIN L16 [get_ports {green[3]}]
set_property PACKAGE_PIN K16 [get_ports {green[4]}]
set_property PACKAGE_PIN M15 [get_ports {green[5]}]
set_property PACKAGE_PIN M16 [get_ports {green[6]}]
set_property PACKAGE_PIN M17 [get_ports {green[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[*]}]

set_property PACKAGE_PIN A21 [get_ports {yellow[0]}]
set_property PACKAGE_PIN E22 [get_ports {yellow[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {yellow[*]}]
```

### 方案2：检查键盘扫描逻辑

检查keyboard.v中的扫描逻辑是否与硬件匹配：

1. **行扫描方式**：确认是否使用正确的扫描方向
2. **信号极性**：确认输入输出信号的极性
3. **防抖时间**：确认防抖时间是否合适

### 方案3：分步调试

#### 第1步：测试基础功能
```verilog
// 临时测试代码 - 强制输出测试
assign yellow = 2'b11;  // 强制黄灯亮
assign green = 8'b10101010;  // 强制绿灯闪烁模式
```

#### 第2步：测试键盘连接
```verilog
// 简单的键盘测试 - 直接将col信号输出到LED
assign green[3:0] = col;  // 将键盘列信号直接显示在LED上
assign row = 4'b0000;     // 强制所有行为低电平
```

#### 第3步：测试数码管
```verilog
// 强制数码管显示测试数字
assign data = 32'h12345678;  // 显示固定数字
```

## 🔧 立即修复步骤

### 第1步：更新约束文件
1. 备份当前的管教.md文件
2. 使用上面提供的正确约束文件
3. 特别注意pil_mode引脚从A16改为W4

### 第2步：重新编译下载
1. 在Vivado中重新综合和实现
2. 检查是否有新的错误
3. 下载到FPGA

### 第3步：测试验证
1. 复位后观察黄色LED是否为"11"
2. 尝试按键盘数字键1
3. 观察数码管是否显示"00000001"

## 🎯 预期结果

修复后的正常行为：
1. **复位后**：数码管显示"00000000"，黄色LED显示"11"
2. **按键盘1**：数码管显示"00000001"
3. **按键盘2**：数码管显示"00000002"
4. **按确认键**：黄色LED变为"01"，数码管清零

## 📋 调试检查清单

- [ ] pil_mode引脚从A16改为W4
- [ ] 键盘引脚按参考约束调整
- [ ] 所有引脚都有对应的IOSTANDARD
- [ ] 时钟约束正确
- [ ] 重新编译无错误
- [ ] 下载成功
- [ ] 复位功能正常
- [ ] 键盘响应正常

按照这个方案修复，您的系统应该能够正常工作！
