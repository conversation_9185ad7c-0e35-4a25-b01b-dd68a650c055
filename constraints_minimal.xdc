# 最简化约束文件 - 用于初期调试
# 只包含最基本的信号，避免复杂的引脚分配问题

#######################################
# 时钟约束（必需）
#######################################
set_property PACKAGE_PIN Y18 [get_ports sys_clk]
set_property IOSTANDARD LVCMOS33 [get_ports sys_clk]
create_clock -add -name sys_clk_pin -period 10.00 -waveform {0 5} [get_ports sys_clk]

#######################################
# 复位信号（必需）
#######################################
set_property PACKAGE_PIN P20 [get_ports sys_rst_n]
set_property IOSTANDARD LVCMOS33 [get_ports sys_rst_n]

#######################################
# 基本控制信号
#######################################
set_property PACKAGE_PIN R1 [get_ports start]
set_property IOSTANDARD LVCMOS15 [get_ports start]

set_property PACKAGE_PIN P1 [get_ports ack]
set_property IOSTANDARD LVCMOS15 [get_ports ack]

set_property PACKAGE_PIN W4 [get_ports pil_mode]
set_property IOSTANDARD LVCMOS33 [get_ports pil_mode]

set_property PACKAGE_PIN R4 [get_ports display_mode]
set_property IOSTANDARD LVCMOS33 [get_ports display_mode]

#######################################
# 4x4矩阵键盘接口
#######################################
# 列输入信号 col[3:0]
set_property PACKAGE_PIN K4 [get_ports {col[0]}]    # K1
set_property PACKAGE_PIN J4 [get_ports {col[1]}]    # K2
set_property PACKAGE_PIN L3 [get_ports {col[2]}]    # K3
set_property PACKAGE_PIN K3 [get_ports {col[3]}]    # K4
set_property IOSTANDARD LVCMOS33 [get_ports {col[*]}]

# 行输出信号 row[3:0]
set_property PACKAGE_PIN M2 [get_ports {row[0]}]    # K5
set_property PACKAGE_PIN K6 [get_ports {row[1]}]    # K6
set_property PACKAGE_PIN J6 [get_ports {row[2]}]    # K7
set_property PACKAGE_PIN J5 [get_ports {row[3]}]    # K8
set_property IOSTANDARD LVCMOS33 [get_ports {row[*]}]

#######################################
# 数码管显示（核心功能）
#######################################
# 段选信号
set_property PACKAGE_PIN F15 [get_ports {smg[0]}]
set_property PACKAGE_PIN F13 [get_ports {smg[1]}]
set_property PACKAGE_PIN F14 [get_ports {smg[2]}]
set_property PACKAGE_PIN F16 [get_ports {smg[3]}]
set_property PACKAGE_PIN E17 [get_ports {smg[4]}]
set_property PACKAGE_PIN C14 [get_ports {smg[5]}]
set_property PACKAGE_PIN C15 [get_ports {smg[6]}]
set_property PACKAGE_PIN E13 [get_ports {smg[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {smg[*]}]

# 位选信号（只约束低4位用于测试）
set_property PACKAGE_PIN C19 [get_ports l_gw]
set_property PACKAGE_PIN E19 [get_ports l_sw]
set_property PACKAGE_PIN D19 [get_ports l_bw]
set_property PACKAGE_PIN F18 [get_ports l_qw]
set_property IOSTANDARD LVCMOS33 [get_ports l_gw]
set_property IOSTANDARD LVCMOS33 [get_ports l_sw]
set_property IOSTANDARD LVCMOS33 [get_ports l_bw]
set_property IOSTANDARD LVCMOS33 [get_ports l_qw]

# 高4位数码管位选
set_property PACKAGE_PIN E18 [get_ports h_gw]
set_property PACKAGE_PIN B20 [get_ports h_sw]
set_property PACKAGE_PIN A20 [get_ports h_bw]
set_property PACKAGE_PIN A18 [get_ports h_qw]
set_property IOSTANDARD LVCMOS33 [get_ports h_gw]
set_property IOSTANDARD LVCMOS33 [get_ports h_sw]
set_property IOSTANDARD LVCMOS33 [get_ports h_bw]
set_property IOSTANDARD LVCMOS33 [get_ports h_qw]

#######################################
# LED指示灯（基本功能）
#######################################
# 红色LED
set_property PACKAGE_PIN N19 [get_ports red]
set_property IOSTANDARD LVCMOS33 [get_ports red]

# 绿色LED（只约束前4个用于测试）
set_property PACKAGE_PIN J17 [get_ports {green[0]}]
set_property PACKAGE_PIN L14 [get_ports {green[1]}]
set_property PACKAGE_PIN L15 [get_ports {green[2]}]
set_property PACKAGE_PIN L16 [get_ports {green[3]}]
set_property PACKAGE_PIN K16 [get_ports {green[4]}]
set_property PACKAGE_PIN M15 [get_ports {green[5]}]
set_property PACKAGE_PIN M16 [get_ports {green[6]}]
set_property PACKAGE_PIN M17 [get_ports {green[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[*]}]

# 黄色LED
set_property PACKAGE_PIN A21 [get_ports {yellow[0]}]
set_property PACKAGE_PIN E22 [get_ports {yellow[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {yellow[*]}]

#######################################
# 配置属性
#######################################
set_property BITSTREAM.CONFIG.UNUSEDPIN PULLUP [current_design]
set_property BITSTREAM.GENERAL.COMPRESS TRUE [current_design]

#######################################
# 使用说明
#######################################
# 这个约束文件用于初期调试，包含了所有必要的信号约束
# 如果仍有问题，可以进一步简化：
# 1. 注释掉键盘相关约束，先测试数码管显示
# 2. 注释掉LED约束，只保留时钟、复位和数码管
# 3. 逐步添加其他功能的约束
