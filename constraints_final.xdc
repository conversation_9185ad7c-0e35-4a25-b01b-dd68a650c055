# 药片装瓶系统最终约束文件
# 基于正确的 Xilinx xc7a100tfgg484-1 引脚分配
# 使用正确的键盘引脚 K1-K8

#######################################
# 时钟约束
#######################################
# 100MHz 系统时钟
set_property PACKAGE_PIN Y18 [get_ports sys_clk]
set_property IOSTANDARD LVCMOS33 [get_ports sys_clk]
create_clock -add -name sys_clk_pin -period 10.00 -waveform {0 5} [get_ports sys_clk]

#######################################
# 复位信号
#######################################
# 使用按键S6作为复位信号（低电平有效）
set_property PACKAGE_PIN P20 [get_ports sys_rst_n]
set_property IOSTANDARD LVCMOS33 [get_ports sys_rst_n]

#######################################
# 控制输入信号
#######################################
# 开始信号 - 使用按键S1
set_property PACKAGE_PIN R1 [get_ports start]
set_property IOSTANDARD LVCMOS15 [get_ports start]

# 药片模式选择 - 使用拨码开关SW0
set_property PACKAGE_PIN W4 [get_ports pil_mode]
set_property IOSTANDARD LVCMOS33 [get_ports pil_mode]

# 确认信号 - 使用按键S2
set_property PACKAGE_PIN P1 [get_ports ack]
set_property IOSTANDARD LVCMOS15 [get_ports ack]

# 显示模式控制 - 使用拨码开关SW1
set_property PACKAGE_PIN R4 [get_ports display_mode]
set_property IOSTANDARD LVCMOS33 [get_ports display_mode]

#######################################
# 4x4矩阵键盘接口（正确引脚）
#######################################
# 列输入信号 col[3:0]
set_property PACKAGE_PIN K4 [get_ports {col[0]}]    # K1
set_property PACKAGE_PIN J4 [get_ports {col[1]}]    # K2
set_property PACKAGE_PIN L3 [get_ports {col[2]}]    # K3
set_property PACKAGE_PIN K3 [get_ports {col[3]}]    # K4
set_property IOSTANDARD LVCMOS33 [get_ports {col[*]}]

# 行输出信号 row[3:0]
set_property PACKAGE_PIN M2 [get_ports {row[0]}]    # K5
set_property PACKAGE_PIN K6 [get_ports {row[1]}]    # K6
set_property PACKAGE_PIN J6 [get_ports {row[2]}]    # K7
set_property PACKAGE_PIN J5 [get_ports {row[3]}]    # K8
set_property IOSTANDARD LVCMOS33 [get_ports {row[*]}]

#######################################
# 数码管显示接口
#######################################
# 位选信号（低电平有效）
# 低4位数码管位选
set_property PACKAGE_PIN C19 [get_ports l_gw]    # A0 - 低位个位
set_property PACKAGE_PIN E19 [get_ports l_sw]    # A1 - 低位十位  
set_property PACKAGE_PIN D19 [get_ports l_bw]    # A2 - 低位百位
set_property PACKAGE_PIN F18 [get_ports l_qw]    # A3 - 低位千位

# 高4位数码管位选
set_property PACKAGE_PIN E18 [get_ports h_gw]    # A4 - 高位个位
set_property PACKAGE_PIN B20 [get_ports h_sw]    # A5 - 高位十位
set_property PACKAGE_PIN A20 [get_ports h_bw]    # A6 - 高位百位
set_property PACKAGE_PIN A18 [get_ports h_qw]    # A7 - 高位千位

set_property IOSTANDARD LVCMOS33 [get_ports l_gw]
set_property IOSTANDARD LVCMOS33 [get_ports l_sw]
set_property IOSTANDARD LVCMOS33 [get_ports l_bw]
set_property IOSTANDARD LVCMOS33 [get_ports l_qw]
set_property IOSTANDARD LVCMOS33 [get_ports h_gw]
set_property IOSTANDARD LVCMOS33 [get_ports h_sw]
set_property IOSTANDARD LVCMOS33 [get_ports h_bw]
set_property IOSTANDARD LVCMOS33 [get_ports h_qw]

# 段选信号（低电平有效）
set_property PACKAGE_PIN F15 [get_ports {smg[0]}]    # CA - A段
set_property PACKAGE_PIN F13 [get_ports {smg[1]}]    # CB - B段
set_property PACKAGE_PIN F14 [get_ports {smg[2]}]    # CC - C段
set_property PACKAGE_PIN F16 [get_ports {smg[3]}]    # CD - D段
set_property PACKAGE_PIN E17 [get_ports {smg[4]}]    # CE - E段
set_property PACKAGE_PIN C14 [get_ports {smg[5]}]    # CF - F段
set_property PACKAGE_PIN C15 [get_ports {smg[6]}]    # CG - G段
set_property PACKAGE_PIN E13 [get_ports {smg[7]}]    # DP - 小数点

set_property IOSTANDARD LVCMOS33 [get_ports {smg[*]}]

#######################################
# LED指示灯接口
#######################################
# 红色LED - 异常停止指示（使用1个红色LED）
set_property PACKAGE_PIN N19 [get_ports red]        # RLD0
set_property IOSTANDARD LVCMOS33 [get_ports red]

# 绿色LED - 工作状态流水灯（8个绿色LED）
set_property PACKAGE_PIN J17 [get_ports {green[0]}]   # GLD0
set_property PACKAGE_PIN L14 [get_ports {green[1]}]   # GLD1
set_property PACKAGE_PIN L15 [get_ports {green[2]}]   # GLD2
set_property PACKAGE_PIN L16 [get_ports {green[3]}]   # GLD3
set_property PACKAGE_PIN K16 [get_ports {green[4]}]   # GLD4
set_property PACKAGE_PIN M15 [get_ports {green[5]}]   # GLD5
set_property PACKAGE_PIN M16 [get_ports {green[6]}]   # GLD6
set_property PACKAGE_PIN M17 [get_ports {green[7]}]   # GLD7

set_property IOSTANDARD LVCMOS33 [get_ports {green[*]}]

# 黄色LED - 设置状态指示（使用2个黄色LED）
set_property PACKAGE_PIN A21 [get_ports {yellow[0]}]  # YLD0
set_property PACKAGE_PIN E22 [get_ports {yellow[1]}]  # YLD1

set_property IOSTANDARD LVCMOS33 [get_ports {yellow[*]}]

#######################################
# 配置属性
#######################################
# 配置未使用的引脚为上拉
set_property BITSTREAM.CONFIG.UNUSEDPIN PULLUP [current_design]

# 配置比特流压缩
set_property BITSTREAM.GENERAL.COMPRESS TRUE [current_design]

#######################################
# 引脚分配总结
#######################################
# 
# 系统信号：
# - 时钟：Y18 (100MHz)
# - 复位：P20 (按键S6)
# - 开始：R1 (按键S1)
# - 确认：P1 (按键S2)
# - 模式：W4 (拨码开关SW0)
# - 显示：R4 (拨码开关SW1)
#
# 矩阵键盘：
# - col[0-3]: K4, J4, L3, K3 (K1-K4)
# - row[0-3]: M2, K6, J6, J5 (K5-K8)
#
# 数码管：
# - 位选A[0-7]: C19, E19, D19, F18, E18, B20, A20, A18
# - 段选CA-CG/DP: F15, F13, F14, F16, E17, C14, C15, E13
#
# LED指示：
# - 红灯：N19 (RLD0)
# - 绿灯：J17-M17 (GLD0-GLD7)
# - 黄灯：A21, E22 (YLD0-YLD1)
#
# 注意：所有信号都使用LVCMOS33标准，按键使用LVCMOS15
