# 药片装瓶系统开发板测试指南

## 一、测试前准备

### 1.1 硬件检查清单
- [ ] FPGA开发板（Minisys实验板）
- [ ] USB下载线
- [ ] 4×4矩阵键盘模块
- [ ] 8位数码管显示模块
- [ ] LED指示灯（红、绿、黄）
- [ ] 拨码开关
- [ ] 按键开关

### 1.2 软件环境
- [ ] Vivado 2018.3或更高版本
- [ ] 项目文件：Top.v, main.v, keyboard.v, nixie_tube.v, light_control.v, data_transform.v
- [ ] 约束文件：constraints.xdc

### 1.3 连接验证
确认以下连接正确：
```
时钟源 → Y18引脚
复位按键S6 → P20引脚
开始按键S1 → R1引脚
确认按键S2 → P1引脚
拨码开关SW0 → W4引脚（药片模式）
拨码开关SW1 → R4引脚（显示模式）
矩阵键盘 → KP1-KP8引脚
数码管 → A0-A7(位选) + CA-CG/DP(段选)
LED灯组 → 对应引脚
```

## 二、项目编译和下载

### 2.1 创建Vivado项目
1. 打开Vivado，创建新项目
2. 选择芯片：xc7a100tfgg484-1
3. 添加所有Verilog源文件
4. 添加约束文件constraints.xdc
5. 设置Top.v为顶层模块

### 2.2 综合和实现
```tcl
# 在Vivado TCL控制台执行
launch_runs synth_1 -jobs 4
wait_on_run synth_1
launch_runs impl_1 -jobs 4
wait_on_run impl_1
launch_runs impl_1 -to_step write_bitstream -jobs 4
wait_on_run impl_1
```

### 2.3 下载到FPGA
1. 连接USB下载线
2. 打开Hardware Manager
3. 检测FPGA设备
4. 下载比特流文件

## 三、系统功能测试

### 3.1 基础功能测试

#### 测试1：系统复位和初始化
**操作步骤：**
1. 按下复位按键S6
2. 观察系统状态

**预期结果：**
- 所有LED熄灭
- 数码管显示"00000000"
- 黄色LED显示设置状态（yellow[1:0] = 2'b11）

#### 测试2：数码管显示测试
**操作步骤：**
1. 观察数码管是否正常扫描显示
2. 检查各位数码管是否能正常点亮

**预期结果：**
- 8位数码管能够正常显示数字
- 扫描频率约为2ms，无明显闪烁

### 3.2 键盘输入测试

#### 测试3：矩阵键盘功能
**操作步骤：**
1. 按下矩阵键盘上的数字键1-9
2. 观察数码管显示变化

**预期结果：**
- 按键1：数码管显示"00000001"
- 按键2：数码管显示"00000002"
- 依此类推...

**键盘布局参考：**
```
1  2  3  功能键
4  5  6  功能键  
7  8  9  功能键
功能键 0 功能键 功能键
```

#### 测试4：多位数输入
**操作步骤：**
1. 连续按下多个数字键，如：1→2→3
2. 观察数码管显示

**预期结果：**
- 数码管应显示"00000123"
- 最大支持3位数输入（999）

### 3.3 参数设置测试

#### 测试5：药瓶总数设置
**操作步骤：**
1. 确认系统在设置模式（黄色LED：11）
2. 使用矩阵键盘输入药瓶总数，如：100
3. 按下确认键S2

**预期结果：**
- 数码管显示输入的数值
- 黄色LED状态变为"01"（进入下一设置项）
- 数码管清零，等待下一个参数输入

#### 测试6：单瓶药片数设置
**操作步骤：**
1. 确认黄色LED状态为"01"
2. 使用矩阵键盘输入单瓶药片数，如：50
3. 按下确认键S2

**预期结果：**
- 数码管显示输入的数值
- 黄色LED状态变为"00"（设置完成）
- 系统进入工作准备状态

### 3.4 工作模式测试

#### 测试7：普通模式测试
**操作步骤：**
1. 确保拨码开关SW0处于OFF位置（普通模式）
2. 完成参数设置后，按下开始键S1
3. 观察系统工作状态

**预期结果：**
- 绿色LED开始流水灯效果（每0.5秒移动一位）
- 数码管显示当前装瓶进度
- 红色LED保持熄灭状态

#### 测试8：定制模式测试
**操作步骤：**
1. 将拨码开关SW0拨到ON位置（定制模式）
2. 重复参数设置和启动过程

**预期结果：**
- 每装完一瓶后，系统暂停
- 黄色LED变为"01"，等待重新设置单瓶药片数
- 可以为每个瓶子设置不同的药片数量

### 3.5 异常处理测试

#### 测试9：参数超限测试
**操作步骤：**
1. 尝试输入超过999的数值
2. 或输入0作为参数

**预期结果：**
- 红色LED点亮
- 系统停止工作
- 需要复位后重新设置

#### 测试10：工作完成测试
**操作步骤：**
1. 设置较小的参数值（如：药瓶数=2，单瓶药片数=3）
2. 启动系统并等待完成

**预期结果：**
- 所有绿色LED同时点亮
- 数码管显示最终完成状态
- 系统停止工作

## 四、高级测试

### 4.1 显示模式切换测试
**操作步骤：**
1. 在工作过程中切换拨码开关SW1
2. 观察数码管显示内容变化

**预期结果：**
- 能够在不同显示内容间切换
- 如：当前瓶数、剩余瓶数、当前瓶内药片数等

### 4.2 长时间运行测试
**操作步骤：**
1. 设置较大的参数值
2. 让系统长时间运行
3. 观察系统稳定性

**预期结果：**
- 系统能够稳定运行
- 计数准确无误
- 显示正常无闪烁

## 五、故障排除

### 5.1 常见问题及解决方案

| 问题现象 | 可能原因 | 解决方案 |
|----------|----------|----------|
| 数码管不显示 | 引脚连接错误 | 检查位选和段选引脚 |
| 按键无响应 | 防抖时间过长 | 检查代码中的防抖参数 |
| LED不亮 | 极性接反 | 确认低电平触发 |
| 键盘输入错误 | 行列扫描问题 | 检查矩阵键盘连接 |
| 系统不复位 | 复位信号问题 | 检查P20引脚连接 |

### 5.2 调试技巧
1. **使用ILA调试**：
   - 添加关键信号到ILA
   - 观察内部状态变化

2. **分模块测试**：
   - 先测试键盘模块
   - 再测试数码管模块
   - 最后测试整体功能

3. **参数调整**：
   - 可以修改代码中的时间参数进行快速测试
   - 如将1秒改为0.1秒

## 六、测试记录表

建议制作测试记录表，记录每项测试的结果：

| 测试项目 | 预期结果 | 实际结果 | 通过/失败 | 备注 |
|----------|----------|----------|-----------|------|
| 系统复位 |          |          |           |      |
| 数码管显示 |        |          |           |      |
| 键盘输入 |          |          |           |      |
| 参数设置 |          |          |           |      |
| 普通模式 |          |          |           |      |
| 定制模式 |          |          |           |      |
| 异常处理 |          |          |           |      |
| 工作完成 |          |          |           |      |

通过系统性的测试，可以确保药片装瓶系统在实际硬件上的正确运行。
