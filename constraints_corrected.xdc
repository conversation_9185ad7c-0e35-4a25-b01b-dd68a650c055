# 药片装瓶系统约束文件
# 完全按照参考约束文件分配引脚

#######################################
# 时钟约束 - 对应参考文件的clock
#######################################
set_property PACKAGE_PIN Y18 [get_ports sys_clk]
set_property IOSTANDARD LVCMOS33 [get_ports sys_clk]
create_clock -add -name sys_clk_pin -period 10.00 -waveform {0 5} [get_ports sys_clk]

#######################################
# 复位信号 - 对应参考文件的reset
#######################################
set_property PACKAGE_PIN P20 [get_ports sys_rst_n]
set_property IOSTANDARD LVCMOS33 [get_ports sys_rst_n]

#######################################
# 控制按键 - 对应参考文件的raw_button
#######################################
# start对应raw_button[0]
set_property PACKAGE_PIN R1 [get_ports start]
set_property IOSTANDARD LVCMOS33 [get_ports start]

# ack对应raw_button[1]
set_property PACKAGE_PIN P1 [get_ports ack]
set_property IOSTANDARD LVCMOS33 [get_ports ack]

# pil_mode对应参考文件的strict_enable
set_property PACKAGE_PIN W4 [get_ports pil_mode]
set_property IOSTANDARD LVCMOS33 [get_ports pil_mode]

# display_mode使用raw_button[2]
set_property PACKAGE_PIN P5 [get_ports display_mode]
set_property IOSTANDARD LVCMOS33 [get_ports display_mode]

#######################################
# 4x4矩阵键盘接口 - 完全按照参考约束
#######################################
# 列输入信号 col[3:0] - 对应keyboard_col_n
set_property PACKAGE_PIN L5 [get_ports {col[0]}]    # keyboard_col_n[0]
set_property PACKAGE_PIN J6 [get_ports {col[1]}]    # keyboard_col_n[1]
set_property PACKAGE_PIN K6 [get_ports {col[2]}]    # keyboard_col_n[2]
set_property PACKAGE_PIN M2 [get_ports {col[3]}]    # keyboard_col_n[3]
set_property IOSTANDARD LVCMOS33 [get_ports {col[*]}]

# 行输出信号 row[3:0] - 对应keyboard_row_n
set_property PACKAGE_PIN K3 [get_ports {row[0]}]    # keyboard_row_n[0]
set_property PACKAGE_PIN L3 [get_ports {row[1]}]    # keyboard_row_n[1]
set_property PACKAGE_PIN J4 [get_ports {row[2]}]    # keyboard_row_n[2]
set_property PACKAGE_PIN K4 [get_ports {row[3]}]    # keyboard_row_n[3]
set_property IOSTANDARD LVCMOS33 [get_ports {row[*]}]

#######################################
# 数码管显示接口 - 完全按照参考约束
#######################################
# 位选信号（低电平有效）- 对应digital_tube_enable_n
set_property PACKAGE_PIN C19 [get_ports l_gw]    # digital_tube_enable_n[0]
set_property PACKAGE_PIN E19 [get_ports l_sw]    # digital_tube_enable_n[1]
set_property PACKAGE_PIN D19 [get_ports l_bw]    # digital_tube_enable_n[2]
set_property PACKAGE_PIN F18 [get_ports l_qw]    # digital_tube_enable_n[3]
set_property PACKAGE_PIN E18 [get_ports h_gw]    # digital_tube_enable_n[4]
set_property PACKAGE_PIN B20 [get_ports h_sw]    # digital_tube_enable_n[5]
set_property PACKAGE_PIN A20 [get_ports h_bw]    # digital_tube_enable_n[6]
set_property PACKAGE_PIN A18 [get_ports h_qw]    # digital_tube_enable_n[7]

set_property IOSTANDARD LVCMOS33 [get_ports l_gw]
set_property IOSTANDARD LVCMOS33 [get_ports l_sw]
set_property IOSTANDARD LVCMOS33 [get_ports l_bw]
set_property IOSTANDARD LVCMOS33 [get_ports l_qw]
set_property IOSTANDARD LVCMOS33 [get_ports h_gw]
set_property IOSTANDARD LVCMOS33 [get_ports h_sw]
set_property IOSTANDARD LVCMOS33 [get_ports h_bw]
set_property IOSTANDARD LVCMOS33 [get_ports h_qw]

# 段选信号（低电平有效）- 对应digital_tube_segment_n
# 注意：参考文件中段选顺序是[6:0]，需要重新映射
set_property PACKAGE_PIN C15 [get_ports {smg[0]}]    # digital_tube_segment_n[0] - A段
set_property PACKAGE_PIN C14 [get_ports {smg[1]}]    # digital_tube_segment_n[1] - B段
set_property PACKAGE_PIN E17 [get_ports {smg[2]}]    # digital_tube_segment_n[2] - C段
set_property PACKAGE_PIN F16 [get_ports {smg[3]}]    # digital_tube_segment_n[3] - D段
set_property PACKAGE_PIN F14 [get_ports {smg[4]}]    # digital_tube_segment_n[4] - E段
set_property PACKAGE_PIN F13 [get_ports {smg[5]}]    # digital_tube_segment_n[5] - F段
set_property PACKAGE_PIN F15 [get_ports {smg[6]}]    # digital_tube_segment_n[6] - G段
set_property PACKAGE_PIN E13 [get_ports {smg[7]}]    # digital_tube_dp_n - 小数点

set_property IOSTANDARD LVCMOS33 [get_ports {smg[*]}]

#######################################
# LED指示灯接口 - 根据参考约束映射
#######################################
# 红色LED - 对应参考文件的funnel_disable (N19)
set_property PACKAGE_PIN N19 [get_ports red]
set_property IOSTANDARD LVCMOS33 [get_ports red]

# 绿色LED - 使用motor_enable和其他可用引脚
# 只有一个引脚J17对应motor_enable，其他使用标准LED引脚
set_property PACKAGE_PIN J17 [get_ports {green[0]}]    # motor_enable
# 其他绿色LED使用标准引脚（需要根据实际硬件确认）
set_property PACKAGE_PIN L14 [get_ports {green[1]}]
set_property PACKAGE_PIN L15 [get_ports {green[2]}]
set_property PACKAGE_PIN L16 [get_ports {green[3]}]
set_property PACKAGE_PIN K16 [get_ports {green[4]}]
set_property PACKAGE_PIN M15 [get_ports {green[5]}]
set_property PACKAGE_PIN M16 [get_ports {green[6]}]
set_property PACKAGE_PIN M17 [get_ports {green[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[*]}]

# 黄色LED - 使用buzzer_audio和其他引脚
set_property PACKAGE_PIN A19 [get_ports {yellow[0]}]    # buzzer_audio
set_property PACKAGE_PIN E22 [get_ports {yellow[1]}]    # 保持原有引脚
set_property IOSTANDARD LVCMOS33 [get_ports {yellow[*]}]

#######################################
# 配置属性
#######################################
set_property BITSTREAM.CONFIG.UNUSEDPIN PULLUP [current_design]
set_property BITSTREAM.GENERAL.COMPRESS TRUE [current_design]

#######################################
# 引脚映射说明
#######################################
# 完全按照参考约束文件映射：
#
# 时钟和复位：
# - sys_clk -> Y18 (clock)
# - sys_rst_n -> P20 (reset)
#
# 控制信号：
# - start -> R1 (raw_button[0])
# - ack -> P1 (raw_button[1])
# - display_mode -> P5 (raw_button[2])
# - pil_mode -> W4 (strict_enable)
#
# 键盘接口：
# - col[0:3] -> L5,J6,K6,M2 (keyboard_col_n[0:3])
# - row[0:3] -> K3,L3,J4,K4 (keyboard_row_n[0:3])
#
# 数码管：
# - 位选: l_gw~h_qw -> C19,E19,D19,F18,E18,B20,A20,A18 (digital_tube_enable_n[0:7])
# - 段选: smg[0:7] -> C15,C14,E17,F16,F14,F13,F15,E13 (digital_tube_segment_n[0:6]+dp_n)
#
# LED输出：
# - red -> N19 (funnel_disable)
# - green[0] -> J17 (motor_enable)
# - yellow[0] -> A19 (buzzer_audio)
