# 修正后的药片装瓶系统约束文件
# 解决按键无响应问题

#######################################
# 时钟约束
#######################################
set_property PACKAGE_PIN Y18 [get_ports sys_clk]
set_property IOSTANDARD LVCMOS33 [get_ports sys_clk]
create_clock -add -name sys_clk_pin -period 10.00 -waveform {0 5} [get_ports sys_clk]

#######################################
# 复位信号
#######################################
set_property PACKAGE_PIN P20 [get_ports sys_rst_n]
set_property IOSTANDARD LVCMOS33 [get_ports sys_rst_n]

#######################################
# 控制输入信号
#######################################
set_property PACKAGE_PIN R1 [get_ports start]
set_property IOSTANDARD LVCMOS33 [get_ports start]

set_property PACKAGE_PIN P1 [get_ports ack]
set_property IOSTANDARD LVCMOS33 [get_ports ack]

# 修正：pil_mode引脚从A16改为W4
set_property PACKAGE_PIN W4 [get_ports pil_mode]
set_property IOSTANDARD LVCMOS33 [get_ports pil_mode]

set_property PACKAGE_PIN R4 [get_ports display_mode]
set_property IOSTANDARD LVCMOS33 [get_ports display_mode]

#######################################
# 4x4矩阵键盘接口 - 根据参考约束修正
#######################################
# 列输入信号 col[3:0] - 参考keyboard_col_n的引脚分配
set_property PACKAGE_PIN L5 [get_ports {col[0]}]
set_property PACKAGE_PIN J6 [get_ports {col[1]}]
set_property PACKAGE_PIN K6 [get_ports {col[2]}]
set_property PACKAGE_PIN M2 [get_ports {col[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {col[*]}]

# 行输出信号 row[3:0] - 参考keyboard_row_n的引脚分配
set_property PACKAGE_PIN K3 [get_ports {row[0]}]
set_property PACKAGE_PIN L3 [get_ports {row[1]}]
set_property PACKAGE_PIN J4 [get_ports {row[2]}]
set_property PACKAGE_PIN K4 [get_ports {row[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {row[*]}]

#######################################
# 数码管显示接口
#######################################
# 位选信号（低电平有效）
set_property PACKAGE_PIN C19 [get_ports l_gw]    # A0 - 低位个位
set_property PACKAGE_PIN E19 [get_ports l_sw]    # A1 - 低位十位  
set_property PACKAGE_PIN D19 [get_ports l_bw]    # A2 - 低位百位
set_property PACKAGE_PIN F18 [get_ports l_qw]    # A3 - 低位千位
set_property PACKAGE_PIN E18 [get_ports h_gw]    # A4 - 高位个位
set_property PACKAGE_PIN B20 [get_ports h_sw]    # A5 - 高位十位
set_property PACKAGE_PIN A20 [get_ports h_bw]    # A6 - 高位百位
set_property PACKAGE_PIN A18 [get_ports h_qw]    # A7 - 高位千位

set_property IOSTANDARD LVCMOS33 [get_ports l_gw]
set_property IOSTANDARD LVCMOS33 [get_ports l_sw]
set_property IOSTANDARD LVCMOS33 [get_ports l_bw]
set_property IOSTANDARD LVCMOS33 [get_ports l_qw]
set_property IOSTANDARD LVCMOS33 [get_ports h_gw]
set_property IOSTANDARD LVCMOS33 [get_ports h_sw]
set_property IOSTANDARD LVCMOS33 [get_ports h_bw]
set_property IOSTANDARD LVCMOS33 [get_ports h_qw]

# 段选信号（低电平有效）
set_property PACKAGE_PIN F15 [get_ports {smg[0]}]    # CA - A段
set_property PACKAGE_PIN F13 [get_ports {smg[1]}]    # CB - B段
set_property PACKAGE_PIN F14 [get_ports {smg[2]}]    # CC - C段
set_property PACKAGE_PIN F16 [get_ports {smg[3]}]    # CD - D段
set_property PACKAGE_PIN E17 [get_ports {smg[4]}]    # CE - E段
set_property PACKAGE_PIN C14 [get_ports {smg[5]}]    # CF - F段
set_property PACKAGE_PIN C15 [get_ports {smg[6]}]    # CG - G段
set_property PACKAGE_PIN E13 [get_ports {smg[7]}]    # DP - 小数点

set_property IOSTANDARD LVCMOS33 [get_ports {smg[*]}]

#######################################
# LED指示灯接口
#######################################
# 红色LED
set_property PACKAGE_PIN N19 [get_ports red]
set_property IOSTANDARD LVCMOS33 [get_ports red]

# 绿色LED（8个）
set_property PACKAGE_PIN J17 [get_ports {green[0]}]
set_property PACKAGE_PIN L14 [get_ports {green[1]}]
set_property PACKAGE_PIN L15 [get_ports {green[2]}]
set_property PACKAGE_PIN L16 [get_ports {green[3]}]
set_property PACKAGE_PIN K16 [get_ports {green[4]}]
set_property PACKAGE_PIN M15 [get_ports {green[5]}]
set_property PACKAGE_PIN M16 [get_ports {green[6]}]
set_property PACKAGE_PIN M17 [get_ports {green[7]}]
set_property IOSTANDARD LVCMOS33 [get_ports {green[*]}]

# 黄色LED（2个）
set_property PACKAGE_PIN A21 [get_ports {yellow[0]}]
set_property PACKAGE_PIN E22 [get_ports {yellow[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {yellow[*]}]

#######################################
# 配置属性
#######################################
set_property BITSTREAM.CONFIG.UNUSEDPIN PULLUP [current_design]
set_property BITSTREAM.GENERAL.COMPRESS TRUE [current_design]

#######################################
# 修正说明
#######################################
# 主要修正内容：
# 1. pil_mode引脚从A16改为W4
# 2. 键盘引脚根据参考约束文件调整：
#    - col引脚使用参考文件的keyboard_col_n引脚
#    - row引脚使用参考文件的keyboard_row_n引脚
# 3. 确保所有信号都有正确的IOSTANDARD设置
# 4. 添加了时钟约束
