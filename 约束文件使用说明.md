# 药片装瓶系统约束文件使用说明

## 概述
本约束文件 `constraints.xdc` 是为北京邮电大学数字逻辑课程设计的药片装瓶系统项目创建的，适用于 Xilinx xc7a100tfgg484-1 FPGA 芯片。

## 硬件映射关系

### 1. 系统基础信号
| 信号名 | 引脚 | 功能描述 | 备注 |
|--------|------|----------|------|
| sys_clk | Y18 | 100MHz系统时钟 | 主时钟源 |
| sys_rst_n | P20 | 系统复位信号 | 按键S6，低电平有效 |

### 2. 控制输入信号
| 信号名 | 引脚 | 硬件 | 功能描述 |
|--------|------|------|----------|
| start | R1 | 按键S1 | 开始药片装瓶过程 |
| ack | P1 | 按键S2 | 确认键盘输入 |
| pil_mode | W4 | 拨码开关SW0 | 选择药片模式（普通/定制） |
| display_mode | R4 | 拨码开关SW1 | 显示模式控制 |

### 3. 矩阵键盘接口（4×4）
| 信号组 | 引脚范围 | 功能 |
|--------|----------|------|
| col[3:0] | KP1-KP4 | 键盘列输入 |
| row[3:0] | KP5-KP8 | 键盘行输出 |

**键盘布局：**
```
1  2  3  功能键
4  5  6  功能键  
7  8  9  功能键
功能键 0 功能键 功能键
```

### 4. 数码管显示接口（8位）
#### 位选信号（低电平有效）
| 信号名 | 引脚 | 对应位置 | 说明 |
|--------|------|----------|------|
| l_gw | C19 | A0 | 低4位个位 |
| l_sw | E19 | A1 | 低4位十位 |
| l_bw | D19 | A2 | 低4位百位 |
| l_qw | F18 | A3 | 低4位千位 |
| h_gw | E18 | A4 | 高4位个位 |
| h_sw | B20 | A5 | 高4位十位 |
| h_bw | A20 | A6 | 高4位百位 |
| h_qw | A18 | A7 | 高4位千位 |

#### 段选信号（低电平有效）
| 信号名 | 引脚 | 对应段 |
|--------|------|---------|
| smg[0] | F15 | CA (A段) |
| smg[1] | F13 | CB (B段) |
| smg[2] | F14 | CC (C段) |
| smg[3] | F16 | CD (D段) |
| smg[4] | E17 | CE (E段) |
| smg[5] | C14 | CF (F段) |
| smg[6] | C15 | CG (G段) |
| smg[7] | E13 | DP (小数点) |

### 5. LED指示灯
#### 红色LED（异常停止指示）
| 信号名 | 引脚 | 功能 |
|--------|------|------|
| red | N19 | 系统异常停止时点亮 |

#### 绿色LED（工作状态流水灯）
| 信号名 | 引脚 | 位置 |
|--------|------|------|
| green[0] | J17 | GLD0 |
| green[1] | L14 | GLD1 |
| green[2] | L15 | GLD2 |
| green[3] | L16 | GLD3 |
| green[4] | K16 | GLD4 |
| green[5] | M15 | GLD5 |
| green[6] | M16 | GLD6 |
| green[7] | M17 | GLD7 |

#### 黄色LED（设置状态指示）
| 信号名 | 引脚 | 功能 |
|--------|------|------|
| yellow[0] | A21 | 设置状态指示1 |
| yellow[1] | E22 | 设置状态指示2 |

## 使用方法

### 1. 在Vivado中使用
1. 将 `constraints.xdc` 文件添加到Vivado项目中
2. 确保约束文件在综合和实现过程中被正确加载
3. 检查引脚分配是否与实际硬件连接一致

### 2. 验证约束
在Vivado中可以通过以下方式验证约束：
```tcl
# 检查时钟约束
report_clocks
# 检查引脚分配
report_io
# 检查时序约束
report_timing_summary
```

### 3. 注意事项
1. **电平标准**：大部分信号使用LVCMOS33，按键使用LVCMOS15
2. **触发方式**：数码管和LED都是低电平触发
3. **防抖处理**：按键防抖已在Verilog代码中实现
4. **时钟频率**：系统设计基于100MHz时钟

## 系统工作流程

### 1. 初始化阶段
- 系统复位后进入设置模式
- 黄色LED指示当前设置状态
- 数码管显示当前设置值

### 2. 参数设置阶段
- 使用矩阵键盘输入药瓶总数
- 按ack键确认输入
- 使用矩阵键盘输入单瓶药片数量
- 按ack键确认输入

### 3. 工作阶段
- 按start键开始装瓶过程
- 绿色LED流水灯显示工作进度
- 数码管实时显示当前状态
- 完成后所有绿色LED点亮

### 4. 异常处理
- 参数超出范围时红色LED点亮
- 系统停止工作并等待复位

## 故障排除

### 常见问题
1. **数码管不显示**：检查位选和段选信号连接
2. **按键无响应**：检查按键引脚分配和防抖设置
3. **LED不亮**：确认LED是低电平触发
4. **键盘输入错误**：检查行列扫描时序

### 调试建议
1. 使用ILA（集成逻辑分析仪）监控关键信号
2. 检查时钟信号是否正常
3. 验证复位信号的极性
4. 确认所有引脚的电平标准设置正确

## 扩展说明
如需修改引脚分配，请注意：
1. 保持信号的电平标准一致性
2. 确保时钟和复位信号的可靠性
3. 考虑信号完整性和EMI问题
4. 更新相应的时序约束
